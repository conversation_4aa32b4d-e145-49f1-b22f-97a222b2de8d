#!/usr/bin/env python3
"""
CSV Processing Script for Conference Data

This script processes CSV files for conference data, extracting conference segment names,
filtering data, and preparing output files for further processing.
"""

import os
import glob
import pandas as pd
import numpy as np
import re
import random
import warnings
import rich_progress
from datetime import datetime

# Suppress SettingWithCopyWarning
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)


def create_gradient_bar(total, desc="Processing"):
    """
    Create a Rich progress bar with gradient colors.

    Args:
        total: Total number of items
        desc: Description for the progress bar

    Returns:
        Rich progress bar instance and update function
    """
    # Define color schemes based on the description
    color_schemes = ["blue", "green", "purple", "orange"]

    # Choose a color scheme based on the process type to ensure consistency within a process
    # but variety between different processes
    color_seed = sum(ord(c) for c in desc) % len(color_schemes)
    color_scheme = color_schemes[color_seed]

    # Create and return the progress bar with the rich_progress module
    progress, update_func = rich_progress.create_progress_bar(total, desc, color_scheme)

    return progress, update_func


def print_status(message, style="info"):
    """
    Print a status message with appropriate styling using rich_progress.

    Args:
        message: The message to print
        style: The style of the message ("info", "success", "error", "warning", "header")
    """
    rich_progress.print_status(message, style)

# Base paths
MASTER_DIR = r"H:/Master Bounces and Unsubs"
POSTPANEL_DIR = os.path.join(MASTER_DIR, "Postpanel Unsubs")
MASTER_BOUNCES_DIR = os.path.join(MASTER_DIR, "Master Bounces and Unsubs")
REPLIED_DIR = os.path.join(MASTER_DIR, "Replied Ext/Prev_Rep_bounces_csv")


def extract_conference_name(path: str) -> str:
    """
    Extract conference segment name from a path using regex.

    Args:
        path: Path string to extract from

    Returns:
        Conference name or prompts for manual input if not found
    """
    # Define a regex pattern to match the desired segment, including spaces
    pattern = r"\\([\w\s-]+\d{4})\\?"

    # Search for the pattern in the path
    match = re.search(pattern, path)

    if match:
        csn = match.group(1)
        print_status(f"Conference Segment Name (CSN): {csn}", "success")
        return csn
    else:
        print_status("Desired segment not found in path.", "warning")

        # Prompt for manual input
        csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ").strip()

        if csn:
            print_status(f"Using manually entered segment: {csn}", "info")
            return csn
        else:
            print_status("No segment name provided.", "error")
            raise ValueError("No conference segment name provided")


def process_mw_unsubs() -> None:
    """Process MailWizz unsubscribers and save to CSV."""
    print("Processing MailWizz unsubscribers...")

    # Generate datetime string for filenames
    datetime_str = datetime.now().strftime("%d%m%Y%H%M%S")

    # Process MW unsubscribers
    os.chdir(os.path.join(POSTPANEL_DIR, "mw"))

    # List all CSV files and concatenate them
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print("No CSV files found in MW directory")
        return

    try:
        df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip',
                                          usecols=['email', 'status', 'date_added'])
                              for f in csv_files], ignore_index=True)

        # Filter for unsubscribed status
        status = ["unsubscribed"]
        unsub_df = df_concat[df_concat['status'].isin(status)]

        # Remove duplicates
        unsub_df1 = unsub_df.drop_duplicates(subset='email')

        # Save intermediate file with datetime
        intermediate_filename = f'mw_list_unsubscriber_{datetime_str}.csv'
        unsub_df1.to_csv(intermediate_filename, encoding='utf-8-sig', index=False)

        # Read back and rename columns
        unsub_df2 = pd.read_csv(intermediate_filename)
        unsub_df2.rename(columns={'email': 'Email', 'status': 'Conference Name',
                                 'date_added': 'DateTime Info'}, inplace=True)

        # Replace values
        unsub_dfg = unsub_df2.apply(lambda x: x.str.replace('unsubscribed', 'Global Unsubscriber'))

        # Save to Postpanel Unsubs directory with datetime
        os.chdir(POSTPANEL_DIR)
        final_filename = f'mw_unsubscribers_{datetime_str}.csv'
        unsub_dfg.to_csv(final_filename, index=False)
        print(f"MailWizz unsubscribers processed successfully: {final_filename}")

    except Exception as e:
        print(f"Error processing MW unsubscribers: {str(e)}")


def process_port1_unsubs() -> None:
    """Process Port1 unsubscribers and save to CSV."""
    print("Processing Port1 unsubscribers...")

    # Generate datetime string for filenames
    datetime_str = datetime.now().strftime("%d%m%Y%H%M%S")

    try:
        os.chdir(os.path.join(POSTPANEL_DIR, "port1"))

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            print("No CSV files found in Port1 directory")
            return

        # Concatenate all CSV files
        unsub_mgport = pd.concat([pd.read_csv(f, on_bad_lines='skip', encoding='latin')
                                 for f in csv_files], ignore_index=True)

        # Rename columns
        unsub_mgport.rename(columns={'Email ID': 'Email', 'To': 'Conference Name',
                                    'Date Info': 'DateTime Info'}, inplace=True)

        # Replace values using regex
        unsub_mgportg = unsub_mgport.replace(to_replace=r".*\(.+?\)", value='Global Unsubscriber', regex=True)
        unsub_mgportg['Conference Name'] = unsub_mgportg['Conference Name'].fillna('Global Unsubscriber')

        # Remove duplicates
        unsub_mgportg.drop_duplicates(subset='Email', inplace=True)

        # Save to Postpanel Unsubs directory with datetime
        os.chdir(POSTPANEL_DIR)
        filename = f'unsubcriber_sheet_{datetime_str}.csv'
        unsub_mgportg.to_csv(filename, mode='w+', index=False)
        print(f"Port1 unsubscribers processed successfully: {filename}")

    except Exception as e:
        print(f"Error processing Port1 unsubscribers: {str(e)}")


def process_global_unsubs(sp_filter: str) -> None:
    """
    Process global unsubscribers and save to CSV.

    Args:
        sp_filter: Conference segment name for filtering
    """
    print("Processing global unsubscribers...")

    try:
        os.chdir(POSTPANEL_DIR)

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            print("No CSV files found in Postpanel Unsubs directory")
            return

        # Concatenate all CSV files
        glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)

        # Clean data
        glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-') if isinstance(x, str) else x)

        # Drop DateTime Info column
        if 'DateTime Info' in glob_unsubs.columns:
            glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)

        # Filter by conference name
        options = ['Global Unsubscriber', sp_filter]
        unsub_df3 = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]

        # Remove duplicates and convert emails to lowercase
        unsub_df3.drop_duplicates(subset='Email', inplace=True, ignore_index=True)
        unsub_df3.drop(['Conference Name'], axis=1, inplace=True)
        unsub_df3[['Email']] = unsub_df3[['Email']].applymap(lambda x: x.lower() if isinstance(x, str) else x)

        # Save to Master Bounces and Unsubs directory
        output_path = os.path.join(MASTER_BOUNCES_DIR, "PP_Global_Unsubscribers.csv")
        unsub_df3.to_csv(output_path, index=False)
        print(f"Global unsubscribers saved to {output_path}")

    except Exception as e:
        print(f"Error processing global unsubscribers: {str(e)}")


def clean_name(name: str) -> str:
    """
    Clean name by removing text after the first comma.

    Args:
        name: Name string to clean

    Returns:
        Cleaned name string
    """
    return str(name).split(',')[0].strip()


def load_sent_emails(path: str) -> pd.DataFrame:
    """
    Load sent emails from the 'sent' folder to filter out previously sent emails.

    Args:
        path: Path to the directory containing the 'sent' folder

    Returns:
        DataFrame containing sent emails, or empty DataFrame if no sent folder/files found
    """
    try:
        sent_dir = os.path.join(path, "sent")

        if not os.path.exists(sent_dir):
            print_status("No 'sent' folder found. Skipping sent emails filter.", "warning")
            return pd.DataFrame()

        # List all CSV files in sent directory
        sent_csv_files = glob.glob(os.path.join(sent_dir, '*.csv'))

        if not sent_csv_files:
            print_status("No CSV files found in 'sent' folder. Skipping sent emails filter.", "warning")
            return pd.DataFrame()

        print_status(f"Found {len(sent_csv_files)} sent files to process for filtering", "info")

        # Concatenate all sent CSV files
        sent_dfs = []
        for file in sent_csv_files:
            try:
                df_temp = pd.read_csv(file, on_bad_lines='skip', low_memory=False)

                # Look for email column with various possible names
                email_columns = ['Email', 'email', 'EMAIL', 'Email Address', 'email_address']
                email_col = None

                for col in email_columns:
                    if col in df_temp.columns:
                        email_col = col
                        break

                if email_col:
                    # Extract only the email column and rename it to 'Email'
                    df_temp = df_temp[[email_col]].rename(columns={email_col: 'Email'})
                    sent_dfs.append(df_temp)
                else:
                    print_status(f"No email column found in {os.path.basename(file)}", "warning")

            except Exception as e:
                print_status(f"Error reading {os.path.basename(file)}: {str(e)}", "warning")
                continue

        if not sent_dfs:
            print_status("No valid sent email data found.", "warning")
            return pd.DataFrame()

        # Concatenate all sent emails
        df_sent = pd.concat(sent_dfs, ignore_index=True)

        # Remove duplicates and convert to lowercase
        df_sent.drop_duplicates(subset='Email', inplace=True)
        df_sent['Email'] = df_sent['Email'].str.lower()

        print_status(f"Loaded {len(df_sent)} unique sent emails for filtering", "success")
        return df_sent

    except Exception as e:
        print_status(f"Error loading sent emails: {str(e)}", "error")
        return pd.DataFrame()


def process_conference_data(path: str, csn: str) -> pd.DataFrame:
    """
    Process conference data from CSV files.

    Args:
        path: Path to the directory containing CSV files
        csn: Conference segment name

    Returns:
        Processed DataFrame
    """
    print(f"Processing conference data for {csn}...")

    # Create a progress bar for the overall process
    process_steps = 5  # Number of major processing steps
    process_bar, update_process = create_gradient_bar(total=process_steps, desc="Processing data")

    try:
        os.chdir(path)

        # Create process directory
        process_dir = os.path.join(path, "process")
        os.makedirs(process_dir, exist_ok=True)
        update_process(1, "Created process directory")  # Step 1 complete

        # List all CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            print(f"No CSV files found in {path}")
            process_bar.stop()
            return pd.DataFrame()

        # Concatenate all CSV files
        df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False)
                              for f in csv_files], ignore_index=True)

        # Standardize column names
        df_concat.rename(columns={'Author Name': 'Name', 'Name ': 'Name'}, inplace=True)

        # Clean names by removing text after comma
        df_concat['Name'] = df_concat['Name'].apply(clean_name)

        # Remove rows with empty Email
        df_concat.dropna(subset='Email', inplace=True)
        update_process(1, "Processed CSV files")  # Step 2 complete

        # Generate datetime string for temporary files
        datetime_str = datetime.now().strftime("%d%m%Y%H%M%S")

        # Save unfiltered data
        unfiltered_path = os.path.join(process_dir, f"{csn}-process_unfiltered_{datetime_str}.csv")
        df_concat.to_csv(unfiltered_path, encoding='utf-8-sig', index=False)

        # Load master files for filtering
        df_hb = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "Master_Hardbounces.csv"),
                           on_bad_lines='skip')
        df_unsubs = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "Master_Unsubscribes.csv"))
        df_rep = pd.read_csv(os.path.join(REPLIED_DIR, f"{csn}_replied_bouncers.csv"))
        df_pp_gl_unsubs = pd.read_csv(os.path.join(MASTER_BOUNCES_DIR, "PP_Global_Unsubscribers.csv"))

        # Load sent emails for filtering
        df_sent = load_sent_emails(path)

        update_process(1, "Loaded master files")  # Step 3 complete

        # Combine unsubscribers
        df_concat_unsubs = pd.concat([df_unsubs, df_pp_gl_unsubs])
        df_concat_unsubs[['Email']] = df_concat_unsubs[['Email']].applymap(
            lambda x: x.lower() if isinstance(x, str) else x)

        # Read back the unfiltered data
        df_concat = pd.read_csv(unfiltered_path, low_memory=False)

        # Filter out hard bounces, unsubscribers, replied, and sent emails
        df_hb_filtered = df_concat[~(df_concat.Email.isin(df_hb.Email))]
        df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered.Email.isin(df_concat_unsubs.Email))]
        df_rep_filtered = df_unsubs_filtered[~(df_unsubs_filtered.Email.isin(df_rep.Email))]

        # Apply sent emails filter if sent data exists
        if not df_sent.empty:
            before_sent_filter = len(df_rep_filtered)
            df = df_rep_filtered[~(df_rep_filtered.Email.isin(df_sent.Email))]
            sent_filtered_count = before_sent_filter - len(df)
            print_status(f"Filtered out {sent_filtered_count} previously sent emails", "info")
        else:
            df = df_rep_filtered
            print_status("No sent emails to filter", "info")

        update_process(1, "Filtered data")  # Step 4 complete

        # Remove Article Title column if it exists
        if 'Article Title' not in df.columns:
            df['Article Title'] = 0
        df.drop(['Article Title'], axis=1, inplace=True)

        # Save deduped data
        deduped_path = os.path.join(process_dir, f"{csn}-process_deduped_{datetime_str}.csv")
        df.to_csv(deduped_path, encoding='utf-8-sig', index=False)

        # Extract only Name and Email columns
        df = pd.read_csv(deduped_path, usecols=["Name", "Email"])

        # Clean data
        df1 = df.replace(r'^\s*$', np.nan, regex=True)
        df2 = df1.fillna('Colleague')

        # Remove duplicates
        result = df2.drop_duplicates(subset='Email')

        # Save processed data
        process_path = os.path.join(process_dir, f"{csn}-process_{datetime_str}.csv")
        result.to_csv(process_path, mode='w+', encoding='utf-8-sig', index=False)

        # Read back the processed data
        df = pd.read_csv(process_path)
        print(f"Processed {len(df)} records for {csn}")

        # Clean up temporary files
        os.remove(deduped_path)
        os.remove(unfiltered_path)
        os.remove(process_path)
        os.rmdir(process_dir)

        # Complete the progress bar
        update_process(1, "Completed processing")  # Step 5 complete
        process_bar.stop()

        return df

    except Exception as e:
        print(f"Error processing conference data: {str(e)}")
        # Close the progress bar in case of error
        process_bar.stop()
        return pd.DataFrame()


def generate_subject_lines(df: pd.DataFrame, csn: str) -> pd.DataFrame:
    """
    Generate random subject lines for each record.

    Args:
        df: DataFrame with records
        csn: Conference segment name

    Returns:
        DataFrame with added subject lines
    """
    print("Generating subject lines...")

    # List of subject line templates
    subj_list = [
        'Invitation to Submit Abstract for Oral Presentation',
        'Invitation to Submit Abstracts for [CCT_CSNAME]',
        'Call for Papers: 20-Minute Oral Presentation at [CCT_CSNAME]',
        'Submit Your Abstract for [CCT_CSNAME]',
        'Oral Presentation Slots Available at [CCT_CSNAME]',
        'Join Us as a Presenter at [CCT_CSNAME]',
        'Abstract Submission for Oral Presentations at [CCT_CSNAME] is OPEN!',
        'Your Expertise Wanted: Call for 20-Minutes Oral Presentation',
        '[CCT_CSNAME]: Now Accepting Abstract Submissions for Oral Presentations',
        'Share Your Research at [CCT_CSNAME]',
        'Invitation to Submit Abstract for 20-Minute Oral Presentation',
        'Present Your Findings at [CCT_CSNAME]',
        'Call for Oral Presentation Abstracts at [CCT_CSNAME]',
        '[CCT_CSNAME]: Call for 20-Minute Oral Presentation Abstracts',
        'Call for 20-Minute Oral Presentation Abstracts Now Open!',
        'Be Part of the Program: Submit Your Abstract Now!',
        'Call for Abstracts: [CCT_CSNAME]',
        'Submit your Research Abstract for the [CCT_CSNAME]',
        'Abstract Submission Open: [CCT_CSNAME]',
        'Submit Your Abstracts for the [CCT_CSNAME]',
        'Invitation to Speak at the [CCT_CSNAME]',
        'Be Our Guest Speaker at the [CCT_CSNAME]',
        'Call for Speakers: [CCT_CSNAME]',
        'Discovering the Future of Technology at [CCT_CSNAME]',
        'The Ultimate Networking Opportunity: [CCT_CSNAME]',
        "Don't Miss Out on [CCT_CSNAME]: Exploring the Latest Trends and Technologies",
        'Join the Conversations at [CCT_CSNAME]: A Dynamic Forum for Ideas and Inspiration'
    ]

    # Replace placeholder with actual conference name
    dynamic_subj_list = [subject.replace("[CCT_CSNAME]", csn) for subject in subj_list]

    # Create a progress bar for generating subject lines
    subject_bar, update_subject = create_gradient_bar(total=1, desc="Generating subject lines")

    # Assign random subject lines
    df["Subject"] = pd.Series(
        random.choices(dynamic_subj_list, k=len(df)),
        index=df.index
    )

    # Update and close the progress bar
    update_subject(1, "Subject lines generated")
    subject_bar.stop()

    return df


def save_output_files(df: pd.DataFrame, csn: str, n_temps: int = 1, create_splits: bool = False) -> int:
    """
    Save output files, splitting the data if needed.

    Args:
        df: DataFrame with records
        csn: Conference segment name
        n_temps: Number of templates/chunks to split into
        create_splits: Whether to create splits of 10000 rows each in the splits folder

    Returns:
        int: Number of files created
    """

    try:
        # Create output directory
        output_dir = os.path.join(os.getcwd(), "output")
        os.makedirs(output_dir, exist_ok=True)

        # Define splits directory path
        splits_dir = os.path.join(output_dir, "splits")

        # Create splits directory only if we're using splits
        if create_splits:
            os.makedirs(splits_dir, exist_ok=True)

        # Get the number of rows in the dataframe
        n_rows = len(df)

        # Maximum rows per file (10,000 as per user preference)
        max_rows_per_file = 10000

        # Track total number of files created
        total_files = 0

        # Generate datetime string in the format: DDMMYYYYHHMMSS
        datetime_str = datetime.now().strftime("%d%m%Y%H%M%S")

        # Handle based on whether we're creating splits or using templates
        if create_splits:
            # Message is now handled by the main function

            # Calculate number of splits needed
            num_splits = (n_rows + max_rows_per_file - 1) // max_rows_per_file

            # Create gradient progress bar
            splits_bar, update_splits = create_gradient_bar(total=num_splits, desc="Creating splits")

            # Process each split
            for i in range(num_splits):
                start_idx = i * max_rows_per_file
                end_idx = min((i + 1) * max_rows_per_file, n_rows)

                # Extract chunk
                chunk = df.iloc[start_idx:end_idx]

                # Save to splits directory with datetime
                output_path = os.path.join(splits_dir, f"{csn}_split_{i+1:03d}_{datetime_str}.csv")
                chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                total_files += 1
                update_splits(1, f"Created split {i+1}/{num_splits}")

            # Stop the progress bar
            splits_bar.stop()

        else:
            # Calculate the size of each chunk based on templates
            chunk_size = n_rows // n_temps if n_temps > 0 else n_rows

            # Create gradient progress bar for templates
            template_bar, update_template = create_gradient_bar(total=n_temps, desc="Creating template files")

            # Split the dataframe into chunks and save them as separate csv files
            for i in range(n_temps):
                start = i * chunk_size
                end = (i + 1) * chunk_size if i < n_temps - 1 else n_rows
                chunk = df[start:end]

                # Save directly to output directory with new naming convention
                if n_temps == 1:
                    # Single file: {csn}_{datetime}.csv
                    output_path = os.path.join(output_dir, f"{csn}_{datetime_str}.csv")
                else:
                    # Multiple files: {csn}_{datetime}_{i+1}.csv
                    output_path = os.path.join(output_dir, f"{csn}_{datetime_str}_{i+1}.csv")

                chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                total_files += 1
                update_template(1, f"Created template {i+1}/{n_temps}")

            # Stop the progress bar
            template_bar.stop()

        return total_files

    except Exception as e:
        print_status(f"Error saving output files: {str(e)}", "error")
        return 0


def calculate_optimal_templates(df: pd.DataFrame) -> int:
    """
    Calculate the optimal number of templates based on row count and estimated file size.

    Args:
        df: DataFrame to analyze

    Returns:
        Optimal number of templates
    """
    row_count = len(df)
    max_rows_per_template = 300000

    # Calculate estimated file size (rough estimation)
    # Assume average of 50 characters per row (Name + Email + some overhead)
    estimated_size_mb = (row_count * 50) / (1024 * 1024)
    max_size_mb = 20

    # Calculate templates needed based on row count
    templates_by_rows = (row_count + max_rows_per_template - 1) // max_rows_per_template

    # Calculate templates needed based on file size
    templates_by_size = max(1, int(estimated_size_mb / max_size_mb) + (1 if estimated_size_mb % max_size_mb > 0 else 0))

    # Use the higher of the two requirements
    optimal_templates = max(templates_by_rows, templates_by_size)

    print_status(f"Data analysis: {row_count:,} rows, estimated size: {estimated_size_mb:.1f}MB", "info")
    print_status(f"Optimal templates calculated: {optimal_templates} (max {max_rows_per_template:,} rows or {max_size_mb}MB per file)", "success")

    return optimal_templates


def get_yes_no_input(prompt: str) -> bool:
    """
    Get a yes/no input from the user.

    Args:
        prompt: The prompt to display to the user

    Returns:
        True for yes, False for no
    """
    while True:
        response = input(f"{prompt} (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        else:
            print("Please enter 'y' or 'n'.")


def main():
    """Main function to run the script."""
    try:
        # Print header
        print_status("CSV Processing Script for Conference Data", "header")

        # Get input path
        path = input("Loc: ")

        # Extract conference segment name
        csn = extract_conference_name(path)
        print_status(f"Processing conference: {csn}", "info")

        # Process unsubscribers
        print_status("Processing unsubscribers...", "info")
        process_mw_unsubs()
        process_port1_unsubs()
        process_global_unsubs(csn)

        # Process conference data
        print_status("Processing conference data...", "info")
        df = process_conference_data(path, csn)

        if not df.empty:
            # Set subject lines to 'n' by default
            include_subject = False
            print_status("Subject lines will not be included (set to default 'n').", "info")

            # Set splits to 'n' by default
            create_splits = False
            print_status("Split output set to default 'n' - will use template-based splitting.", "info")

            # Calculate optimal number of templates automatically
            n_temps = calculate_optimal_templates(df)

            # Save output files
            print_status("Saving output files...", "info")
            total_files = save_output_files(df, csn, n_temps, create_splits)

            if total_files > 0:
                file_type = "split" if create_splits else "template"
                print_status(f"Successfully saved {len(df)} records to {total_files} {file_type} files", "success")
                print_status(f"Total records processed: {len(df)}", "success")

        print_status("Processing completed successfully!", "success")

    except Exception as e:
        print_status(f"Error: {str(e)}", "error")
        print_status("Process terminated with errors.", "error")


if __name__ == "__main__":
    main()

